{"version": 2, "builds": [{"src": "dist/server.js", "use": "@vercel/node", "config": {"includeFiles": ["dist/**", "prisma/schema.prisma", "prisma/**", "package.json", "node_modules/.prisma/**"]}}], "routes": [{"src": "/(.*)", "headers": {"Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET,OPTIONS,PATCH,DELETE,POST,PUT", "Access-Control-Allow-Headers": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization"}, "dest": "/dist/server.js"}], "env": {"NODE_ENV": "production", "PRISMA_GENERATE": "true"}}