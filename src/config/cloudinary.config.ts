import { v2 as cloudinary } from 'cloudinary';
import { env } from './env.config';
import logger from './logger.config';

// Configure Cloudinary
cloudinary.config({
  cloud_name: env.CLOUDINARY_CLOUD_NAME,
  api_key: env.CLOUDINARY_API_KEY,
  api_secret: env.CLOUDINARY_API_SECRET,
  secure: true,
});

// Initialize Cloudinary
export const initCloudinary = (): void => {
  try {
    logger.info('Cloudinary initialized successfully');
  } catch (error) {
    logger.error('Error initializing Cloudinary:', error);
  }
};

export default cloudinary;
