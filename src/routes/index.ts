import { Router, Request, Response } from 'express';
import authRoutes from './auth.routes';
import userRoutes from './user.routes';
import onboardingRoutes from './onboarding.routes';
import sessionRoutes from './session.routes';
import quizRoutes from './quiz.routes';
import pollRoutes from './poll.routes';
import dashboardRoutes from './dashboard.routes';
import contentRoutes from './content.routes';

const router = Router();

// Define allowed origins
const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:8080',
  'https://joining-dots-frontend.vercel.app',
  'https://joining-dots-admin-2910abhishek-2910abhisheks-projects.vercel.app',
  'https://joining-dots-backend-beta.vercel.app',
];

// Handle OPTIONS requests globally
router.options('/*', (req: Request, res: Response) => {
  const origin = req.headers.origin;

  if (origin && allowedOrigins.includes(origin)) {
    res.header('Access-Control-Allow-Origin', origin);
  }

  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
  res.header(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization, Content-Length, X-Requested-With',
  );
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(204);
});

// Health check endpoint
router.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/onboarding', onboardingRoutes);
router.use('/sessions', sessionRoutes);
router.use('/quizzes', quizRoutes);
router.use('/poll', pollRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/content', contentRoutes);

export default router;
