import { Router } from 'express';
import {
  uploadContent,
  getContentById,
  getSessionContent,
  updateContent,
  deleteContent,
} from '../controllers/content.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { validateRequest } from '../middleware/validate.middleware';
import { validateContentAccess, validateSessionForContent } from '../middleware/content.middleware';
import { validateContentForm } from '../middleware/content-form.middleware';
import { contentUpload } from '../middleware/upload.middleware';
import {
  getContentByIdSchema,
  getSessionContentSchema,
  updateContentSchema,
  deleteContentSchema,
} from '../validations/content.validation';
import { RequestHandler } from 'express';

const router = Router();

// Upload content to a session (admin only)
router.post(
  '/',
  authenticateToken as RequestHandler,
  contentUpload.single('file'),
  validateContentForm as <PERSON>questHand<PERSON>,
  validateSess<PERSON><PERSON><PERSON><PERSON>ontent as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  uploadContent as <PERSON><PERSON><PERSON><PERSON><PERSON>,
);

// Get content by ID
router.get(
  '/:contentId',
  authenticateToken as RequestHandler,
  validateRequest(getContentByIdSchema),
  validateContentAccess as RequestHandler,
  getContentById as RequestHandler,
);

// Get all content for a session
router.get(
  '/session/:sessionId',
  authenticateToken as RequestHandler,
  validateRequest(getSessionContentSchema),
  getSessionContent as RequestHandler,
);

// Update content metadata
router.put(
  '/:contentId',
  authenticateToken as RequestHandler,
  validateRequest(updateContentSchema),
  validateContentAccess as RequestHandler,
  updateContent as RequestHandler,
);

// Delete content
router.delete(
  '/:contentId',
  authenticateToken as RequestHandler,
  validateRequest(deleteContentSchema),
  validateContentAccess as RequestHandler,
  deleteContent as RequestHandler,
);

export default router;
