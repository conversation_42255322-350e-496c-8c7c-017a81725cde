import { Router } from 'express';
import {
  createQuiz,
  getQuizzes,
  getQuizById,
  addQuestionsToQuiz,
  joinQuiz,
  submitQuizResponse,
  updateQuiz,
  deleteQuiz,
  deleteQuestion,
  updateQuestion,
  getQuizLeaderboard,
  getQuizResults,
  getPublicLeaderboard,
  getGlobalLeaderboard,
} from '../controllers/quiz.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { isAdmin } from '../middleware/admin.middleware';
import { validateRequest } from '../middleware/validate.middleware';
import { validateQuizAccess } from '../middleware/quiz.middleware';
import {
  createQuizSchema,
  addQuestionsSchema,
  joinQuizSchema,
  submitQuizResponseSchema,
  updateQuizSchema,
  updateQuestionSchema,
} from '../validations/quiz.validation';
import { RequestHandler } from 'express';

const router = Router();

// Protected route to create a quiz (admin only)
router.post(
  '/',
  authenticateToken as RequestHandler,
  isAdmin as RequestHand<PERSON>,
  validateRequest(createQuizSchema),
  createQuiz,
);

// Get all quizzes for a session
router.get('/', authenticateToken as RequestHandler, getQuizzes);

// Get quiz by ID
router.get(
  '/:quizId',
  authenticateToken as RequestHandler,
  validateQuizAccess as RequestHandler,
  getQuizById,
);

// Route to add multiple questions to a quiz (admin only)
router.post(
  '/:quizId/questions',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  validateRequest(addQuestionsSchema),
  addQuestionsToQuiz,
);

// Route to join a quiz
router.post(
  '/:quizId/join',
  authenticateToken as RequestHandler,
  validateRequest(joinQuizSchema),
  joinQuiz,
);

// Route to submit quiz responses
router.post(
  '/:quizId/submit',
  authenticateToken as RequestHandler,
  validateQuizAccess as RequestHandler,
  validateRequest(submitQuizResponseSchema),
  submitQuizResponse,
);

// Route to update a quiz (admin only)
router.put(
  '/:quizId',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  validateRequest(updateQuizSchema),
  updateQuiz,
);

// Route to update a question (admin only)
router.put(
  '/:quizId/questions/:questionId',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  validateRequest(updateQuestionSchema),
  updateQuestion,
);

// Route to delete a quiz (admin only)
router.delete(
  '/:quizId',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  deleteQuiz,
);

// Route to delete a question (admin only)
router.delete(
  '/:quizId/questions/:questionId',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  deleteQuestion,
);

// Route to get quiz leaderboard
router.get(
  '/:quizId/leaderboard',
  authenticateToken as RequestHandler,
  validateQuizAccess as RequestHandler,
  getQuizLeaderboard,
);

// Route to get quiz results (admin only)
router.get(
  '/:quizId/results',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  getQuizResults,
);

// Public route to get global leaderboard across all quizzes (no authentication required)
router.get('/leaderboard', getGlobalLeaderboard);

// Public route to get quiz leaderboard (no authentication required)
router.get('/public-leaderboard/:quizId', getPublicLeaderboard);

export default router;
