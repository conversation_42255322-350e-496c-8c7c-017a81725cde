import { Router } from 'express';
import { RequestHandler } from 'express';
import {
  getPollById,
  joinPoll,
  quickCreatePoll,
  addPollQuestion,
  endPollQuestion,
  getPolls,
  submitResponse,
  createPoll,
} from '../controllers/poll.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { validatePollRequest } from '../middleware/poll.validate.middleware';
import {
  joinPollSchema,
  quickCreatePollSchema,
  addPollQuestionSchema,
  submitPollResponseSchema,
  createPollSchema,
} from '../validations/poll.validation';
import { isAdmin } from '../middleware/admin.middleware';

const router = Router();

// Get all polls
router.get('/', authenticateToken as RequestHandler, getPolls as unknown as RequestHandler);

// Standard poll creation
router.post(
  '/',
  authenticateToken as RequestHandler,
  isAdmin as RequestHand<PERSON>,
  validatePollRequest(createPollSchema),
  createPoll as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>,
);

// Quick create a poll (Mentimeter-style)
router.post(
  '/create',
  authenticateToken as RequestHandler,
  isAdmin as <PERSON>quest<PERSON>and<PERSON>,
  validatePoll<PERSON>equest(quickCreatePollSchema),
  quickCreatePoll as unknown as RequestHandler,
);

// Join a poll with code
router.post(
  '/join',
  authenticateToken as RequestHandler,
  validatePollRequest(joinPollSchema),
  joinPoll as unknown as RequestHandler,
);

// Submit response to a poll
router.post(
  '/:pollId/response',
  authenticateToken as RequestHandler,
  validatePollRequest(submitPollResponseSchema),
  submitResponse as unknown as RequestHandler,
);

// Get a poll by ID
router.get(
  '/:pollId',
  authenticateToken as RequestHandler,
  // isAdmin as RequestHandler,
  getPollById as unknown as RequestHandler,
);

// Add a question to an existing poll
router.post(
  '/question',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  validatePollRequest(addPollQuestionSchema),
  addPollQuestion as unknown as RequestHandler,
);

// End a poll question and show results
router.post(
  '/:pollId/end-question',
  authenticateToken as RequestHandler,
  isAdmin as RequestHandler,
  endPollQuestion as unknown as RequestHandler,
);

export default router;
