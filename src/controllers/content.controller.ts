import { Request, Response, NextFunction } from 'express';
import { PrismaClient, ContentType } from '@prisma/client';
import { RequestHandler } from 'express';
import HttpException from '../utils/http-exception';
import logger from '../config/logger.config';
import { uploadToCloudinary, deleteFromCloudinary } from '../services/cloudinary.service';

const prisma = new PrismaClient();

/**
 * Upload content to a session
 * @route POST /api/content
 * @access Private (Admin only)
 */
export const uploadContent: RequestHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    // Log the request body and file for debugging
    logger.info('Request body:', req.body);
    logger.info('Request file:', req.file);

    const { title, sessionId, type } = req.body;
    const userId = req.user?.id;
    const file = req.file;

    if (!file) {
      throw new HttpException(400, 'No file uploaded');
    }

    if (!title || !sessionId) {
      throw new HttpException(400, 'Title and sessionId are required');
    }

    // Determine content type from file if not provided
    let contentType: ContentType;

    if (type && Object.values(ContentType).includes(type as ContentType)) {
      contentType = type as ContentType;
    } else {
      // Determine from mimetype
      if (file.mimetype.startsWith('image/')) {
        contentType = 'IMAGE';
      } else if (file.mimetype.startsWith('video/')) {
        contentType = 'VIDEO';
      } else if (file.mimetype === 'application/pdf') {
        contentType = 'PDF';
      } else {
        contentType = 'TEXT';
      }
    }

    // Upload file to Cloudinary
    const folder = `sessions/${sessionId}/content`;
    const uploadResult = await uploadToCloudinary(file.buffer, folder, contentType);

    // Create content record in database
    const content = await prisma.content.create({
      data: {
        title,
        url: uploadResult.secure_url,
        type: contentType,
        session: {
          connect: {
            id: sessionId,
          },
        },
        // By default, the content creator can edit
        canEdit: {
          connect: {
            id: userId,
          },
        },
      },
      include: {
        session: {
          select: {
            title: true,
          },
        },
      },
    });

    res.status(201).json({
      message: 'Content uploaded successfully',
      content: {
        id: content.id,
        title: content.title,
        url: content.url,
        type: content.type,
        sessionId: content.sessionId,
        sessionTitle: content.session?.title,
        createdAt: content.createdAt,
      },
    });
  } catch (error) {
    logger.error('Error uploading content:', error);
    next(error);
  }
};

/**
 * Get content by ID
 * @route GET /api/content/:contentId
 * @access Private (Users with access)
 */
export const getContentById: RequestHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { contentId } = req.params;

    const content = await prisma.content.findUnique({
      where: { id: contentId },
      include: {
        session: {
          select: {
            id: true,
            title: true,
          },
        },
        canView: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        canEdit: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!content) {
      throw new HttpException(404, 'Content not found');
    }

    res.status(200).json({
      content,
    });
  } catch (error) {
    logger.error('Error getting content:', error);
    next(error);
  }
};

/**
 * Get all content for a session
 * @route GET /api/content/session/:sessionId
 * @access Private (Session participants)
 */
export const getSessionContent: RequestHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { sessionId } = req.params;
    const { page = 1, limit = 10, type } = req.query;

    // Build where clause
    const where: {
      sessionId: string;
      type?: ContentType;
    } = {
      sessionId,
    };

    // Filter by content type if provided
    if (
      type &&
      typeof type === 'string' &&
      Object.values(ContentType).includes(type as ContentType)
    ) {
      where.type = type as ContentType;
    }

    // Calculate pagination
    const skip = (Number(page) - 1) * Number(limit);

    // Get total count for pagination
    const totalCount = await prisma.content.count({ where });

    // Get content for session
    const content = await prisma.content.findMany({
      where,
      skip,
      take: Number(limit),
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        canView: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        canEdit: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    res.status(200).json({
      content,
      pagination: {
        total: totalCount,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalCount / Number(limit)),
      },
    });
  } catch (error) {
    logger.error('Error getting session content:', error);
    next(error);
  }
};

/**
 * Update content metadata
 * @route PUT /api/content/:contentId
 * @access Private (Admin or users with edit permission)
 */
export const updateContent: RequestHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { contentId } = req.params;
    const { title, canView, canEdit } = req.body;

    // Prepare update data
    const updateData: {
      title?: string;
    } = {};
    if (title) updateData.title = title;

    // Update content
    const content = await prisma.content.update({
      where: { id: contentId },
      data: {
        ...updateData,
        // Update canView if provided
        ...(canView && {
          canView: {
            set: [], // Clear existing connections
            connect: canView.map((id: string) => ({ id })),
          },
        }),
        // Update canEdit if provided
        ...(canEdit && {
          canEdit: {
            set: [], // Clear existing connections
            connect: canEdit.map((id: string) => ({ id })),
          },
        }),
      },
      include: {
        session: {
          select: {
            id: true,
            title: true,
          },
        },
        canView: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        canEdit: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    res.status(200).json({
      message: 'Content updated successfully',
      content,
    });
  } catch (error) {
    logger.error('Error updating content:', error);
    next(error);
  }
};

/**
 * Delete content
 * @route DELETE /api/content/:contentId
 * @access Private (Admin or users with edit permission)
 */
export const deleteContent: RequestHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { contentId } = req.params;

    // Get content to get the URL for Cloudinary deletion
    const content = await prisma.content.findUnique({
      where: { id: contentId },
    });

    if (!content) {
      throw new HttpException(404, 'Content not found');
    }

    // Extract public ID from Cloudinary URL
    const urlParts = content.url.split('/');
    const publicIdWithExtension = urlParts[urlParts.length - 1];
    const publicId = publicIdWithExtension.split('.')[0];
    const folderPath = urlParts[urlParts.length - 2];
    const fullPublicId = `${folderPath}/${publicId}`;

    // Delete from Cloudinary
    await deleteFromCloudinary(fullPublicId, content.type);

    // Delete from database
    await prisma.content.delete({
      where: { id: contentId },
    });

    res.status(200).json({
      message: 'Content deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting content:', error);
    next(error);
  }
};
