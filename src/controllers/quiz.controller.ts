import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import logger from '../config/logger.config';
import HttpException from '../utils/http-exception';

const prisma = new PrismaClient();

export const createQuiz = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      title,
      sessionId,
      timeLimitSeconds,
      pointsPerQuestion,
      passingPercentage,
      totalMarks,
      questions,
    } = req.body;

    // Check if the session exists
    const session = await prisma.session.findUnique({
      where: { id: sessionId },
    });

    if (!session) {
      throw new HttpException(404, 'Session not found');
    }

    // Check if a quiz with the same title already exists in the session
    const existingQuiz = await prisma.quiz.findFirst({
      where: {
        title: title,
        sessionId: sessionId,
      },
    });

    if (existingQuiz) {
      res
        .status(409)
        .json({ success: false, message: 'A quiz with this title already exists in the session.' });
      return; // Early return if quiz already exists
    }

    // Create the quiz
    const quiz = await prisma.quiz.create({
      data: {
        title,
        session: { connect: { id: sessionId } },
        timeLimitSeconds,
        pointsPerQuestion,
        passingScore: (passingPercentage / 100) * totalMarks, // Calculate passing score based on percentage
        totalMarks,
        retryQuiz: req.body.retryQuiz || false, // Add this line
      },
    });

    // Create questions associated with the quiz if provided
    if (questions && questions.length > 0) {
      await Promise.all(
        questions.map(async (question: any, index: number) => {
          await prisma.question.create({
            data: {
              text: question.text,
              type: question.type,
              options: question.options ? question.options.join(',') : null,
              correctAnswer: question.correctAnswer,
              order: index + 1,
              quiz: { connect: { id: quiz.id } },
              marks: question.marks, // Add this line to include marks
            },
          });
        }),
      );
    }

    res.status(201).json({
      success: true,
      message: 'Quiz created successfully',
      data: quiz,
    });
  } catch (error) {
    logger.error('Error creating quiz:', error);

    // Check if the error is an instance of HttpException
    if (error instanceof HttpException) {
      res.status(error.status).json({ success: false, message: error.message });
    } else {
      // Handle other types of errors
      res.status(500).json({ success: false, message: 'An unexpected error occurred' });
    }
  }
};

// Get all quizzes for a specific session
export const getQuizzes = async (req: Request, res: Response): Promise<void> => {
  const { sessionId } = req.query; // Get sessionId from query parameters
  const userId = req.user?.id;
  const isAdmin = req.user?.role === 'ADMIN';

  try {
    // Validate sessionId
    if (!sessionId) {
      res.status(400).json({ success: false, message: 'Session ID is required' });
      return;
    }

    // If not admin, check if user is a participant in the session
    if (!isAdmin) {
      const sessionParticipation = await prisma.session.findFirst({
        where: {
          id: String(sessionId),
          participants: {
            some: {
              id: userId,
            },
          },
        },
      });

      if (!sessionParticipation) {
        res.status(403).json({
          success: false,
          message: 'You must be a participant in this session to view its quizzes',
        });
        return;
      }
    }

    // Fetch quizzes for the specified session
    const quizzes = await prisma.quiz.findMany({
      where: {
        sessionId: String(sessionId), // Ensure sessionId is a string
      },
      include: {
        questions: {
          select: {
            id: true,
            text: true,
            type: true,
            order: true,
            marks: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      data: quizzes,
    });
  } catch (error) {
    logger.error('Error retrieving quizzes:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Get quiz by ID
export const getQuizById = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params;

  try {
    // Fetch the quiz details
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      include: {
        questions: true,
      },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return;
    }

    res.status(200).json({
      success: true,
      data: quiz,
    });
  } catch (error) {
    logger.error('Error retrieving quiz by ID:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Add multiple questions to a quiz
export const addQuestionsToQuiz = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params; // Get quiz ID from request parameters
  const questions = req.body; // Get questions array from request body
  const skippedQuestions: string[] = []; // Array to hold skipped question texts

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return;
    }

    // Get existing questions to determine the next order number
    const existingQuestions = await prisma.question.findMany({
      where: { quizId },
      orderBy: { order: 'asc' },
    });

    // Determine the next available order number
    let nextOrderNumber =
      existingQuestions.length > 0 ? Math.max(...existingQuestions.map(q => q.order)) + 1 : 1;

    // Create questions in bulk
    for (const question of questions) {
      const existingQuestion = await prisma.question.findFirst({
        where: {
          text: question.text.toLowerCase(), // Convert to lowercase
          quizId, // Ensure the question is associated with the correct quiz
        },
      });

      if (existingQuestion) {
        skippedQuestions.push(question.text); // Add to skipped questions
        continue; // Skip to the next question
      }

      // Create the question
      await prisma.question.create({
        data: {
          text: question.text.toLowerCase(), // Store in lowercase
          type: question.type,
          options: question.options
            ? question.options.map((option: string) => option.toLowerCase()).join(',')
            : null, // Convert options to lowercase
          correctAnswer: question.correctAnswer.toString().toLowerCase(), // Store correctAnswer as a lowercase string
          order: nextOrderNumber, // Set the new order number
          quiz: { connect: { id: quizId } }, // Associate the question with the quiz
          imageUrl: question.imageUrl, // Add imageUrl here
          timeTaken: question.timeTaken, // Include timeTaken here
          marks: question.marks, // Include marks here
        },
      });

      // Increment the order number for the next question
      nextOrderNumber++;
    }

    res.status(201).json({
      success: true,
      message: 'Questions added successfully',
      skipped: skippedQuestions, // Return the skipped questions
    });
  } catch (error) {
    logger.error('Error adding questions to quiz:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Join a quiz
export const joinQuiz = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params; // Get quiz ID from request parameters
  const userId = req.user?.id; // Get user ID from the request (assumed to be set by auth middleware)

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return; // Early return if quiz does not exist
    }

    // Check if the user is already participating in the quiz
    const existingParticipation = await prisma.quizResponse.findFirst({
      where: {
        quizId,
        userId,
      },
    });

    if (existingParticipation) {
      res
        .status(409)
        .json({ success: false, message: 'You are already participating in this quiz.' });
      return;
    }

    // Create a new quiz response for the user
    const quizResponse = await prisma.quizResponse.create({
      data: {
        quiz: { connect: { id: quizId } },
        user: { connect: { id: userId } },
      },
    });

    res.status(200).json({
      success: true,
      message: 'Successfully joined the quiz.',
      data: quizResponse,
    });
  } catch (error) {
    logger.error('Error joining quiz:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Submit quiz responses
export const submitQuizResponse = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params; // Get quiz ID from request parameters
  const userId = req.user?.id; // Get user ID from the request (assumed to be set by auth middleware)
  const { answers, attemptTime, timeTaken } = req.body; // Get answers and attempt time from request body

  try {
    // Check if the quiz exists and fetch total marks
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      include: {
        questions: true, // Include questions
      },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return; // Early return if quiz does not exist
    }

    // Calculate total marks and marks obtained
    let marksObtained = 0;
    // Define an interface for the question result
    // interface QuestionResult {
    //   questionId: string;
    //   questionText: string;
    //   userAnswer: string;
    //   correctAnswer: string | null;
    //   isCorrect: boolean;
    //   marks: number;
    //   marksObtained: number;
    //   timeTaken: number;
    // }

    // Then use the interface when declaring the array
    const questionResults: Array<{
      questionId: string;
      questionText: string;
      userAnswer: string;
      correctAnswer: string | null;
      isCorrect: boolean;
      marks: number;
      marksObtained: number;
      timeTaken: number;
    }> = [];

    quiz.questions.forEach(question => {
      const correctAnswer = question.correctAnswer;
      const questionMark = question.marks || quiz.pointsPerQuestion || 0;

      if (correctAnswer !== null) {
        const normalizedCorrectAnswer = correctAnswer
          .toString()
          .split(',')
          .map((ans: string) => ans.trim().toLowerCase())
          .sort()
          .join(',');
        const userAnswer = answers[question.id]
          ?.split(',')
          .map((ans: string) => ans.trim().toLowerCase())
          .sort()
          .join(',');

        const isCorrect = userAnswer === normalizedCorrectAnswer;

        if (isCorrect) {
          marksObtained += questionMark;
        }

        // Store detailed result for each question
        questionResults.push({
          questionId: question.id,
          questionText: question.text,
          userAnswer: answers[question.id] || '',
          correctAnswer: question.correctAnswer,
          isCorrect,
          marks: questionMark,
          marksObtained: isCorrect ? questionMark : 0,
          timeTaken: timeTaken[question.id] || 0,
        });
      }
    });

    // Calculate total time taken for the quiz
    const totalTimeTaken = Object.values(timeTaken).reduce(
      (total: number, time: unknown) => total + (time as number),
      0,
    );

    // Create a new quiz response record
    const quizResponse = await prisma.quizResponse.create({
      data: {
        quiz: { connect: { id: quizId } },
        user: { connect: { id: userId } },
        completedAt: new Date(attemptTime), // Store the attempt time
        answers: JSON.stringify(answers), // Store the user's answers as a JSON string
        totalScore: marksObtained, // Store the total score
      },
    });

    res.status(200).json({
      success: true,
      message: 'Quiz submitted successfully.',
      data: {
        marksObtained,
        totalMarks: quiz.totalMarks,
        totalTimeTaken,
        passingScore: quiz.passingScore,
        passed: marksObtained >= (quiz.passingScore || 0),
        questionResults,
        quizResponse: {
          id: quizResponse.id,
          quizId: quizResponse.quizId,
          userId: quizResponse.userId,
          completedAt: quizResponse.completedAt,
          answers: quizResponse.answers,
          createdAt: quizResponse.createdAt,
        },
      },
    });
  } catch (error) {
    logger.error('Error submitting quiz response:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Update quiz
export const updateQuiz = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params;
  const { title, timeLimitSeconds, pointsPerQuestion, passingScore, questions } = req.body;

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return;
    }

    // Update the quiz's basic details
    const updatedQuiz = await prisma.quiz.update({
      where: { id: quizId },
      data: {
        title,
        timeLimitSeconds,
        pointsPerQuestion,
        passingScore,
      },
    });

    // If questions are provided, update them
    if (questions && Array.isArray(questions)) {
      await Promise.all(
        questions.map(async (question: any, index: number) => {
          if (question.id) {
            // Update existing question
            await prisma.question.update({
              where: { id: question.id },
              data: {
                text: question.text,
                type: question.type,
                options: question.options ? question.options.join(',') : null,
                correctAnswer: question.correctAnswer,
                timeTaken: question.timeTaken,
                imageUrl: question.imageUrl,
              },
            });
          } else {
            // Create new question if no ID is provided
            await prisma.question.create({
              data: {
                text: question.text,
                type: question.type,
                options: question.options ? question.options.join(',') : null,
                correctAnswer: question.correctAnswer,
                quiz: { connect: { id: quizId } }, // Associate with the quiz
                timeTaken: question.timeTaken,
                imageUrl: question.imageUrl,
                order: index + 1, // Set the order based on the index
              },
            });
          }
        }),
      );
    }

    res.status(200).json({
      success: true,
      message: 'Quiz updated successfully',
      data: updatedQuiz,
    });
  } catch (error) {
    logger.error('Error updating quiz:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Delete quiz
export const deleteQuiz = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params;

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return;
    }

    // Delete all quiz responses associated with the quiz
    await prisma.quizResponse.deleteMany({
      where: { quizId },
    });

    // Delete all questions associated with the quiz
    await prisma.question.deleteMany({
      where: { quizId },
    });

    // Delete the quiz
    await prisma.quiz.delete({
      where: { id: quizId },
    });

    res.status(200).json({
      success: true,
      message: 'Quiz deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting quiz:', error);

    // Check if the error is an instance of HttpException
    if (error instanceof HttpException) {
      res.status(error.status).json({ success: false, message: error.message });
    } else {
      // Handle other types of errors
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Delete a question from a quiz
export const deleteQuestion = async (req: Request, res: Response): Promise<void> => {
  const { quizId, questionId } = req.params;

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
    });

    if (!quiz) {
      throw new HttpException(404, 'Quiz not found');
    }

    // Check if the question exists
    const question = await prisma.question.findUnique({
      where: { id: questionId },
    });

    if (!question) {
      throw new HttpException(404, 'Question not found');
    }

    // Delete the question
    await prisma.question.delete({
      where: { id: questionId },
    });

    res.status(200).json({
      success: true,
      message: 'Question deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting question:', error);
    if (error instanceof HttpException) {
      res.status(error.status).json({ success: false, message: error.message });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Update a question in a quiz
export const updateQuestion = async (req: Request, res: Response): Promise<void> => {
  const { quizId, questionId } = req.params;
  const { text, type, options, correctAnswer } = req.body;

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
    });

    if (!quiz) {
      throw new HttpException(404, 'Quiz not found');
    }

    // Check if the question exists
    const question = await prisma.question.findUnique({
      where: { id: questionId },
    });

    if (!question) {
      throw new HttpException(404, 'Question not found');
    }

    // Update the question
    const updatedQuestion = await prisma.question.update({
      where: { id: questionId },
      data: {
        text,
        type,
        options: options ? options.join(',') : null, // Convert options to a comma-separated string
        correctAnswer,
      },
    });

    res.status(200).json({
      success: true,
      message: 'Question updated successfully',
      data: updatedQuestion,
    });
  } catch (error) {
    logger.error('Error updating question:', error);
    if (error instanceof HttpException) {
      res.status(error.status).json({ success: false, message: error.message });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get leaderboard for a specific quiz
export const getQuizLeaderboard = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params;

  try {
    // Fetch quiz responses for the specified quiz and group by user
    const leaderboard = await prisma.quizResponse.groupBy({
      by: ['userId'],
      where: { quizId },
      _max: {
        totalScore: true, // Get the maximum score for each user
      },
    });

    // Extract user IDs from the leaderboard
    const userIds = leaderboard.map(response => response.userId);

    // Fetch user details for the leaderboard
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    // Map the results to the desired format
    const formattedLeaderboard = leaderboard.map(response => {
      const user = users.find(u => u.id === response.userId);
      return {
        userId: user?.id,
        userName: user?.name,
        score: response._max.totalScore || 0, // Default to 0 if no score
      };
    });

    res.status(200).json({
      success: true,
      data: formattedLeaderboard,
    });
  } catch (error) {
    logger.error('Error retrieving quiz leaderboard:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Get detailed quiz results including individual answers
export const getQuizResults = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params;

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      include: {
        questions: true,
      },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return;
    }

    // Get all responses for this quiz
    const responses = await prisma.quizResponse.findMany({
      where: { quizId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Format the results
    const results = responses.map(response => {
      // Parse the answers JSON string
      const answers = response.answers ? JSON.parse(response.answers) : {};

      // Map each question to the user's answer
      const questionAnswers = quiz.questions.map(question => {
        const userAnswer = answers[question.id] || '';
        const questionMark = question.marks || quiz.pointsPerQuestion || 0;
        const isCorrect = userAnswer.toLowerCase() === (question.correctAnswer || '').toLowerCase();

        return {
          questionId: question.id,
          questionText: question.text,
          userAnswer,
          correctAnswer: question.correctAnswer,
          isCorrect,
          marks: questionMark,
          marksObtained: isCorrect ? questionMark : 0,
        };
      });

      // Calculate total marks obtained
      const totalMarksObtained = questionAnswers.reduce(
        (total, q) => total + (q.marksObtained || 0),
        0,
      );

      return {
        userId: response.user.id,
        userName: response.user.name,
        userEmail: response.user.email,
        totalScore: response.totalScore || totalMarksObtained,
        completedAt: response.completedAt,
        answers: questionAnswers,
        passed: (response.totalScore || totalMarksObtained) >= (quiz.passingScore || 0),
      };
    });

    res.status(200).json({
      success: true,
      data: {
        quizId: quiz.id,
        quizTitle: quiz.title,
        totalMarks: quiz.totalMarks,
        passingScore: quiz.passingScore,
        results,
      },
    });
  } catch (error) {
    logger.error('Error retrieving quiz results:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Get public leaderboard for a specific quiz (no authentication required)
export const getPublicLeaderboard = async (req: Request, res: Response): Promise<void> => {
  const { quizId } = req.params;

  try {
    // Check if the quiz exists
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      select: { id: true, title: true },
    });

    if (!quiz) {
      res.status(404).json({ success: false, message: 'Quiz not found' });
      return;
    }

    // Fetch quiz responses for the specified quiz and group by user
    const leaderboard = await prisma.quizResponse.groupBy({
      by: ['userId'],
      where: { quizId },
      _max: {
        totalScore: true, // Get the maximum score for each user
      },
    });

    // Extract user IDs from the leaderboard
    const userIds = leaderboard.map(response => response.userId);

    // Fetch user details for the leaderboard
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Map the results to the desired format
    const formattedLeaderboard = leaderboard.map(response => {
      const user = users.find(u => u.id === response.userId);
      return {
        userId: user?.id,
        userName: user?.name,
        score: response._max.totalScore || 0, // Default to 0 if no score
      };
    });

    // Sort by score in descending order
    formattedLeaderboard.sort((a, b) => (b.score || 0) - (a.score || 0));

    res.status(200).json({
      success: true,
      data: {
        quizId: quiz.id,
        quizTitle: quiz.title,
        leaderboard: formattedLeaderboard,
      },
    });
  } catch (error) {
    logger.error('Error retrieving public leaderboard:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

// Get global leaderboard across all quizzes (no authentication required)
export const getGlobalLeaderboard = async (_req: Request, res: Response): Promise<void> => {
  try {
    // Get top performers across all quizzes
    const leaderboard = await prisma.quizResponse.groupBy({
      by: ['userId'],
      _sum: {
        totalScore: true, // Sum up scores across all quizzes
      },
      orderBy: {
        _sum: {
          totalScore: 'desc',
        },
      },
      take: 50, // Limit to top 50 performers
    });

    // Extract user IDs from the leaderboard
    const userIds = leaderboard.map(response => response.userId);

    // Fetch user details for the leaderboard
    const users = await prisma.user.findMany({
      where: {
        id: { in: userIds },
      },
      select: {
        id: true,
        name: true,
      },
    });

    // Map the results to the desired format
    const formattedLeaderboard = leaderboard.map(response => {
      const user = users.find(u => u.id === response.userId);
      return {
        userId: user?.id,
        userName: user?.name,
        totalScore: response._sum.totalScore || 0, // Default to 0 if no score
      };
    });

    res.status(200).json({
      success: true,
      data: {
        leaderboard: formattedLeaderboard,
      },
    });
  } catch (error) {
    logger.error('Error retrieving global leaderboard:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
};
