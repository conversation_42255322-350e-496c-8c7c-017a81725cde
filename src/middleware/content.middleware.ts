import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import HttpException from '../utils/http-exception';
import logger from '../config/logger.config';

const prisma = new PrismaClient();

/**
 * Middleware to validate content access
 * Checks if the user has permission to view or edit the content
 */
export const validateContentAccess = async (
  req: Request,
  _res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { contentId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new HttpException(401, 'Unauthorized');
    }

    // Get content with access permissions
    const content = await prisma.content.findUnique({
      where: { id: contentId },
      include: {
        canView: {
          select: {
            id: true,
          },
        },
        canEdit: {
          select: {
            id: true,
          },
        },
        session: {
          select: {
            createdById: true,
            participants: {
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (!content) {
      throw new HttpException(404, 'Content not found');
    }

    // Check if user is admin (creator of the session)
    const isAdmin = content.session?.createdById === userId;

    // Check if user can view the content
    const canView =
      isAdmin ||
      content.canView.some(user => user.id === userId) ||
      content.session?.participants.some(participant => participant.id === userId);

    if (!canView) {
      throw new HttpException(403, 'You do not have permission to access this content');
    }

    // Check if user can edit the content (for PUT/DELETE requests)
    if (
      (req.method === 'PUT' || req.method === 'DELETE') &&
      !isAdmin &&
      !content.canEdit.some(user => user.id === userId)
    ) {
      throw new HttpException(403, 'You do not have permission to modify this content');
    }

    // Add content to request for later use
    req.content = content.session
      ? {
          ...content,
          session: content.session
            ? {
                createdById: content.session.createdById,
                participants: content.session.participants,
              }
            : undefined,
          canView: content.canView,
          canEdit: content.canEdit,
        }
      : undefined;
    next();
  } catch (error) {
    logger.error('Error validating content access:', error);
    next(error);
  }
};

/**
 * Middleware to validate session access for content upload
 * Checks if the user has permission to upload content to the session
 */
export const validateSessionForContent = async (
  req: Request,
  _res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { sessionId } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      throw new HttpException(401, 'Unauthorized');
    }

    // Get session
    const session = await prisma.session.findUnique({
      where: { id: sessionId },
      select: {
        id: true,
        createdById: true,
      },
    });

    if (!session) {
      throw new HttpException(404, 'Session not found');
    }

    // Only session creator (admin) can upload content
    if (session.createdById !== userId) {
      throw new HttpException(403, 'Only the session creator can upload content to this session');
    }

    next();
  } catch (error) {
    logger.error('Error validating session for content:', error);
    next(error);
  }
};
