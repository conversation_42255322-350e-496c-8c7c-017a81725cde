import multer from 'multer';
import HttpException from '../utils/http-exception';

// Configure multer for memory storage
const storage = multer.memoryStorage();

// File filter to allow only CSV and Excel files for user uploads
const csvFileFilter = (
  _req: any, // Using any here as the Request type is complex with user extensions
  file: Express.Multer.File,
  cb: multer.FileFilterCallback,
) => {
  if (
    file.mimetype === 'text/csv' ||
    file.mimetype === 'application/vnd.ms-excel' ||
    file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ) {
    cb(null, true);
  } else {
    cb(new HttpException(400, 'Only CSV and Excel files are allowed'));
  }
};

// File filter for content uploads (images, videos, PDFs, etc.)
const contentFileFilter = (
  _req: any, // Using any here as the Request type is complex with user extensions
  file: Express.Multer.File,
  cb: multer.FileFilterCallback,
) => {
  // Allow images
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  }
  // Allow videos
  else if (file.mimetype.startsWith('video/')) {
    cb(null, true);
  }
  // Allow PDFs
  else if (file.mimetype === 'application/pdf') {
    cb(null, true);
  }
  // Allow text files
  else if (
    file.mimetype === 'text/plain' ||
    file.mimetype === 'text/markdown' ||
    file.mimetype === 'application/msword' ||
    file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ) {
    cb(null, true);
  } else {
    cb(new HttpException(400, 'Only images, videos, PDFs, and text documents are allowed'));
  }
};

// Create multer instance for CSV uploads
export const upload = multer({
  storage,
  fileFilter: csvFileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
});

// Create multer instance for content uploads
export const contentUpload = multer({
  storage,
  fileFilter: contentFileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit for content files
  },
});
