import cloudinary from '../config/cloudinary.config';
import { ContentType } from '@prisma/client';
import logger from '../config/logger.config';
import { Readable } from 'stream';

/**
 * Upload a file to Cloudinary
 * @param file - The file buffer to upload
 * @param folder - The folder to upload to (e.g., 'sessions/123/content')
 * @param contentType - The type of content (IMAGE, PDF, TEXT, VIDEO)
 * @returns The Cloudinary upload result
 */
interface CloudinaryUploadResult {
  public_id: string;
  secure_url: string;
  resource_type: string;
  format: string;
  [key: string]: any;
}

export const uploadToCloudinary = async (
  file: Buffer,
  folder: string,
  contentType: ContentType,
): Promise<CloudinaryUploadResult> => {
  try {
    // Set resource type based on content type
    let resourceType: 'auto' | 'video' | 'image' | 'raw' = 'auto';
    if (contentType === 'VIDEO') {
      resourceType = 'video';
    } else if (contentType === 'IMAGE') {
      resourceType = 'image';
    } else if (contentType === 'PDF') {
      resourceType = 'raw';
    } else if (contentType === 'TEXT') {
      resourceType = 'raw';
    }

    // Upload to Cloudinary
    const result = await new Promise<CloudinaryUploadResult>((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream(
        {
          folder,
          resource_type: resourceType,
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result as CloudinaryUploadResult);
          }
        },
      );

      // Convert buffer to stream and pipe to uploadStream
      const bufferStream = Readable.from(file);
      bufferStream.pipe(uploadStream);
    });

    return result;
  } catch (error) {
    logger.error('Error uploading to Cloudinary:', error);
    throw error;
  }
};

/**
 * Delete a file from Cloudinary
 * @param publicId - The public ID of the file to delete
 * @param contentType - The type of content (IMAGE, PDF, TEXT, VIDEO)
 * @returns The Cloudinary deletion result
 */
interface CloudinaryDeleteResult {
  result: string;
  [key: string]: any;
}

export const deleteFromCloudinary = async (
  publicId: string,
  contentType: ContentType,
): Promise<CloudinaryDeleteResult> => {
  try {
    // Set resource type based on content type
    let resourceType: 'image' | 'video' | 'raw' = 'image';
    if (contentType === 'VIDEO') {
      resourceType = 'video';
    } else if (contentType === 'PDF' || contentType === 'TEXT') {
      resourceType = 'raw';
    }

    // Delete from Cloudinary
    const result = await cloudinary.uploader.destroy(publicId, {
      resource_type: resourceType,
    });

    return result;
  } catch (error) {
    logger.error('Error deleting from Cloudinary:', error);
    throw error;
  }
};
