# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
package-lock.json
pnpm-lock.yaml

# Environment variables
.env
.env.local
.env.*.local
.env.production
.env.development

# Build output
build/

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log

# Coverage directory used by tools like istanbul
coverage/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Prisma
prisma/*.db
prisma/migrations/

# Cursor Rules
/.cursor
.vercel
.env*.local
