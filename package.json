{"name": "joining_dots_backend", "version": "1.0.0", "description": "A production-grade Node.js backend with Express, Prisma, and TypeScript", "main": "dist/server.js", "type": "commonjs", "prisma": {"seed": "ts-node prisma/seed.ts"}, "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx}\"", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:studio": "prisma studio", "seed": "ts-node prisma/seed.ts", "prepare": "husky", "pre-commit": "lint-staged", "push": "npm run build", "postinstall": "prisma generate"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix"]}, "keywords": ["node", "express", "typescript", "prisma", "postgresql"], "author": "", "license": "ISC", "engines": {"node": ">=14.0.0"}, "packageManager": "pnpm@10.5.1", "dependencies": {"@prisma/client": "^6.4.1", "@types/bcrypt": "^5.0.2", "@types/nodemailer": "^6.4.17", "@types/socket.io": "^3.0.2", "@types/uuid": "^9.0.8", "@types/xlsx": "^0.0.36", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "compression": "^1.8.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "envalid": "^8.0.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "1.4.5-lts.1", "nodemailer": "^6.10.0", "socket.io": "^4.8.1", "twilio": "^5.4.5", "uuid": "^9.0.1", "winston": "^3.17.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/ejs": "^3.1.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.5", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.56.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.5.2", "prisma": "^6.4.1", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}}