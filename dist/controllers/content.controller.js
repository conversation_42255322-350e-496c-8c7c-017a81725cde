"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteContent = exports.updateContent = exports.getSessionContent = exports.getContentById = exports.uploadContent = void 0;
const client_1 = require("@prisma/client");
const http_exception_1 = __importDefault(require("../utils/http-exception"));
const logger_config_1 = __importDefault(require("../config/logger.config"));
const cloudinary_service_1 = require("../services/cloudinary.service");
const prisma = new client_1.PrismaClient();
const uploadContent = async (req, res, next) => {
    var _a, _b;
    try {
        logger_config_1.default.info('Request body:', req.body);
        logger_config_1.default.info('Request file:', req.file);
        const { title, sessionId, type } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const file = req.file;
        if (!file) {
            throw new http_exception_1.default(400, 'No file uploaded');
        }
        if (!title || !sessionId) {
            throw new http_exception_1.default(400, 'Title and sessionId are required');
        }
        let contentType;
        if (type && Object.values(client_1.ContentType).includes(type)) {
            contentType = type;
        }
        else {
            if (file.mimetype.startsWith('image/')) {
                contentType = 'IMAGE';
            }
            else if (file.mimetype.startsWith('video/')) {
                contentType = 'VIDEO';
            }
            else if (file.mimetype === 'application/pdf') {
                contentType = 'PDF';
            }
            else {
                contentType = 'TEXT';
            }
        }
        const folder = `sessions/${sessionId}/content`;
        const uploadResult = await (0, cloudinary_service_1.uploadToCloudinary)(file.buffer, folder, contentType);
        const content = await prisma.content.create({
            data: {
                title,
                url: uploadResult.secure_url,
                type: contentType,
                session: {
                    connect: {
                        id: sessionId,
                    },
                },
                canEdit: {
                    connect: {
                        id: userId,
                    },
                },
            },
            include: {
                session: {
                    select: {
                        title: true,
                    },
                },
            },
        });
        res.status(201).json({
            message: 'Content uploaded successfully',
            content: {
                id: content.id,
                title: content.title,
                url: content.url,
                type: content.type,
                sessionId: content.sessionId,
                sessionTitle: (_b = content.session) === null || _b === void 0 ? void 0 : _b.title,
                createdAt: content.createdAt,
            },
        });
    }
    catch (error) {
        logger_config_1.default.error('Error uploading content:', error);
        next(error);
    }
};
exports.uploadContent = uploadContent;
const getContentById = async (req, res, next) => {
    try {
        const { contentId } = req.params;
        const content = await prisma.content.findUnique({
            where: { id: contentId },
            include: {
                session: {
                    select: {
                        id: true,
                        title: true,
                    },
                },
                canView: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                canEdit: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        if (!content) {
            throw new http_exception_1.default(404, 'Content not found');
        }
        res.status(200).json({
            content,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error getting content:', error);
        next(error);
    }
};
exports.getContentById = getContentById;
const getSessionContent = async (req, res, next) => {
    try {
        const { sessionId } = req.params;
        const { page = 1, limit = 10, type } = req.query;
        const where = {
            sessionId,
        };
        if (type &&
            typeof type === 'string' &&
            Object.values(client_1.ContentType).includes(type)) {
            where.type = type;
        }
        const skip = (Number(page) - 1) * Number(limit);
        const totalCount = await prisma.content.count({ where });
        const content = await prisma.content.findMany({
            where,
            skip,
            take: Number(limit),
            orderBy: {
                createdAt: 'desc',
            },
            include: {
                canView: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                canEdit: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        res.status(200).json({
            content,
            pagination: {
                total: totalCount,
                page: Number(page),
                limit: Number(limit),
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    }
    catch (error) {
        logger_config_1.default.error('Error getting session content:', error);
        next(error);
    }
};
exports.getSessionContent = getSessionContent;
const updateContent = async (req, res, next) => {
    try {
        const { contentId } = req.params;
        const { title, canView, canEdit } = req.body;
        const updateData = {};
        if (title)
            updateData.title = title;
        const content = await prisma.content.update({
            where: { id: contentId },
            data: {
                ...updateData,
                ...(canView && {
                    canView: {
                        set: [],
                        connect: canView.map((id) => ({ id })),
                    },
                }),
                ...(canEdit && {
                    canEdit: {
                        set: [],
                        connect: canEdit.map((id) => ({ id })),
                    },
                }),
            },
            include: {
                session: {
                    select: {
                        id: true,
                        title: true,
                    },
                },
                canView: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                canEdit: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        res.status(200).json({
            message: 'Content updated successfully',
            content,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error updating content:', error);
        next(error);
    }
};
exports.updateContent = updateContent;
const deleteContent = async (req, res, next) => {
    try {
        const { contentId } = req.params;
        const content = await prisma.content.findUnique({
            where: { id: contentId },
        });
        if (!content) {
            throw new http_exception_1.default(404, 'Content not found');
        }
        const urlParts = content.url.split('/');
        const publicIdWithExtension = urlParts[urlParts.length - 1];
        const publicId = publicIdWithExtension.split('.')[0];
        const folderPath = urlParts[urlParts.length - 2];
        const fullPublicId = `${folderPath}/${publicId}`;
        await (0, cloudinary_service_1.deleteFromCloudinary)(fullPublicId, content.type);
        await prisma.content.delete({
            where: { id: contentId },
        });
        res.status(200).json({
            message: 'Content deleted successfully',
        });
    }
    catch (error) {
        logger_config_1.default.error('Error deleting content:', error);
        next(error);
    }
};
exports.deleteContent = deleteContent;
//# sourceMappingURL=content.controller.js.map