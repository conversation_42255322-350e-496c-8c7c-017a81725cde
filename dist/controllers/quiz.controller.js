"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGlobalLeaderboard = exports.getPublicLeaderboard = exports.getQuizResults = exports.getQuizLeaderboard = exports.updateQuestion = exports.deleteQuestion = exports.deleteQuiz = exports.updateQuiz = exports.submitQuizResponse = exports.joinQuiz = exports.addQuestionsToQuiz = exports.getQuizById = exports.getQuizzes = exports.createQuiz = void 0;
const client_1 = require("@prisma/client");
const logger_config_1 = __importDefault(require("../config/logger.config"));
const http_exception_1 = __importDefault(require("../utils/http-exception"));
const prisma = new client_1.PrismaClient();
const createQuiz = async (req, res) => {
    try {
        const { title, sessionId, timeLimitSeconds, pointsPerQuestion, passingPercentage, totalMarks, questions, } = req.body;
        const session = await prisma.session.findUnique({
            where: { id: sessionId },
        });
        if (!session) {
            throw new http_exception_1.default(404, 'Session not found');
        }
        const existingQuiz = await prisma.quiz.findFirst({
            where: {
                title: title,
                sessionId: sessionId,
            },
        });
        if (existingQuiz) {
            res
                .status(409)
                .json({ success: false, message: 'A quiz with this title already exists in the session.' });
            return;
        }
        const quiz = await prisma.quiz.create({
            data: {
                title,
                session: { connect: { id: sessionId } },
                timeLimitSeconds,
                pointsPerQuestion,
                passingScore: (passingPercentage / 100) * totalMarks,
                totalMarks,
                retryQuiz: req.body.retryQuiz || false,
            },
        });
        if (questions && questions.length > 0) {
            await Promise.all(questions.map(async (question, index) => {
                await prisma.question.create({
                    data: {
                        text: question.text,
                        type: question.type,
                        options: question.options ? question.options.join(',') : null,
                        correctAnswer: question.correctAnswer,
                        order: index + 1,
                        quiz: { connect: { id: quiz.id } },
                        marks: question.marks,
                    },
                });
            }));
        }
        res.status(201).json({
            success: true,
            message: 'Quiz created successfully',
            data: quiz,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error creating quiz:', error);
        if (error instanceof http_exception_1.default) {
            res.status(error.status).json({ success: false, message: error.message });
        }
        else {
            res.status(500).json({ success: false, message: 'An unexpected error occurred' });
        }
    }
};
exports.createQuiz = createQuiz;
const getQuizzes = async (req, res) => {
    var _a, _b;
    const { sessionId } = req.query;
    const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
    const isAdmin = ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'ADMIN';
    try {
        if (!sessionId) {
            res.status(400).json({ success: false, message: 'Session ID is required' });
            return;
        }
        if (!isAdmin) {
            const sessionParticipation = await prisma.session.findFirst({
                where: {
                    id: String(sessionId),
                    participants: {
                        some: {
                            id: userId,
                        },
                    },
                },
            });
            if (!sessionParticipation) {
                res.status(403).json({
                    success: false,
                    message: 'You must be a participant in this session to view its quizzes',
                });
                return;
            }
        }
        const quizzes = await prisma.quiz.findMany({
            where: {
                sessionId: String(sessionId),
            },
            include: {
                questions: {
                    select: {
                        id: true,
                        text: true,
                        type: true,
                        order: true,
                        marks: true,
                    },
                },
            },
        });
        res.status(200).json({
            success: true,
            data: quizzes,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error retrieving quizzes:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.getQuizzes = getQuizzes;
const getQuizById = async (req, res) => {
    const { quizId } = req.params;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
            include: {
                questions: true,
            },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        res.status(200).json({
            success: true,
            data: quiz,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error retrieving quiz by ID:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.getQuizById = getQuizById;
const addQuestionsToQuiz = async (req, res) => {
    const { quizId } = req.params;
    const questions = req.body;
    const skippedQuestions = [];
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        const existingQuestions = await prisma.question.findMany({
            where: { quizId },
            orderBy: { order: 'asc' },
        });
        let nextOrderNumber = existingQuestions.length > 0 ? Math.max(...existingQuestions.map(q => q.order)) + 1 : 1;
        for (const question of questions) {
            const existingQuestion = await prisma.question.findFirst({
                where: {
                    text: question.text.toLowerCase(),
                    quizId,
                },
            });
            if (existingQuestion) {
                skippedQuestions.push(question.text);
                continue;
            }
            await prisma.question.create({
                data: {
                    text: question.text.toLowerCase(),
                    type: question.type,
                    options: question.options
                        ? question.options.map((option) => option.toLowerCase()).join(',')
                        : null,
                    correctAnswer: question.correctAnswer.toString().toLowerCase(),
                    order: nextOrderNumber,
                    quiz: { connect: { id: quizId } },
                    imageUrl: question.imageUrl,
                    timeTaken: question.timeTaken,
                    marks: question.marks,
                },
            });
            nextOrderNumber++;
        }
        res.status(201).json({
            success: true,
            message: 'Questions added successfully',
            skipped: skippedQuestions,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error adding questions to quiz:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.addQuestionsToQuiz = addQuestionsToQuiz;
const joinQuiz = async (req, res) => {
    var _a;
    const { quizId } = req.params;
    const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        const existingParticipation = await prisma.quizResponse.findFirst({
            where: {
                quizId,
                userId,
            },
        });
        if (existingParticipation) {
            res
                .status(409)
                .json({ success: false, message: 'You are already participating in this quiz.' });
            return;
        }
        const quizResponse = await prisma.quizResponse.create({
            data: {
                quiz: { connect: { id: quizId } },
                user: { connect: { id: userId } },
            },
        });
        res.status(200).json({
            success: true,
            message: 'Successfully joined the quiz.',
            data: quizResponse,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error joining quiz:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.joinQuiz = joinQuiz;
const submitQuizResponse = async (req, res) => {
    var _a;
    const { quizId } = req.params;
    const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
    const { answers, attemptTime, timeTaken } = req.body;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
            include: {
                questions: true,
            },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        let marksObtained = 0;
        const questionResults = [];
        quiz.questions.forEach(question => {
            var _a;
            const correctAnswer = question.correctAnswer;
            const questionMark = question.marks || quiz.pointsPerQuestion || 0;
            if (correctAnswer !== null) {
                const normalizedCorrectAnswer = correctAnswer
                    .toString()
                    .split(',')
                    .map((ans) => ans.trim().toLowerCase())
                    .sort()
                    .join(',');
                const userAnswer = (_a = answers[question.id]) === null || _a === void 0 ? void 0 : _a.split(',').map((ans) => ans.trim().toLowerCase()).sort().join(',');
                const isCorrect = userAnswer === normalizedCorrectAnswer;
                if (isCorrect) {
                    marksObtained += questionMark;
                }
                questionResults.push({
                    questionId: question.id,
                    questionText: question.text,
                    userAnswer: answers[question.id] || '',
                    correctAnswer: question.correctAnswer,
                    isCorrect,
                    marks: questionMark,
                    marksObtained: isCorrect ? questionMark : 0,
                    timeTaken: timeTaken[question.id] || 0,
                });
            }
        });
        const totalTimeTaken = Object.values(timeTaken).reduce((total, time) => total + time, 0);
        const quizResponse = await prisma.quizResponse.create({
            data: {
                quiz: { connect: { id: quizId } },
                user: { connect: { id: userId } },
                completedAt: new Date(attemptTime),
                answers: JSON.stringify(answers),
                totalScore: marksObtained,
            },
        });
        res.status(200).json({
            success: true,
            message: 'Quiz submitted successfully.',
            data: {
                marksObtained,
                totalMarks: quiz.totalMarks,
                totalTimeTaken,
                passingScore: quiz.passingScore,
                passed: marksObtained >= (quiz.passingScore || 0),
                questionResults,
                quizResponse: {
                    id: quizResponse.id,
                    quizId: quizResponse.quizId,
                    userId: quizResponse.userId,
                    completedAt: quizResponse.completedAt,
                    answers: quizResponse.answers,
                    createdAt: quizResponse.createdAt,
                },
            },
        });
    }
    catch (error) {
        logger_config_1.default.error('Error submitting quiz response:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.submitQuizResponse = submitQuizResponse;
const updateQuiz = async (req, res) => {
    const { quizId } = req.params;
    const { title, timeLimitSeconds, pointsPerQuestion, passingScore, questions } = req.body;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        const updatedQuiz = await prisma.quiz.update({
            where: { id: quizId },
            data: {
                title,
                timeLimitSeconds,
                pointsPerQuestion,
                passingScore,
            },
        });
        if (questions && Array.isArray(questions)) {
            await Promise.all(questions.map(async (question, index) => {
                if (question.id) {
                    await prisma.question.update({
                        where: { id: question.id },
                        data: {
                            text: question.text,
                            type: question.type,
                            options: question.options ? question.options.join(',') : null,
                            correctAnswer: question.correctAnswer,
                            timeTaken: question.timeTaken,
                            imageUrl: question.imageUrl,
                        },
                    });
                }
                else {
                    await prisma.question.create({
                        data: {
                            text: question.text,
                            type: question.type,
                            options: question.options ? question.options.join(',') : null,
                            correctAnswer: question.correctAnswer,
                            quiz: { connect: { id: quizId } },
                            timeTaken: question.timeTaken,
                            imageUrl: question.imageUrl,
                            order: index + 1,
                        },
                    });
                }
            }));
        }
        res.status(200).json({
            success: true,
            message: 'Quiz updated successfully',
            data: updatedQuiz,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error updating quiz:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.updateQuiz = updateQuiz;
const deleteQuiz = async (req, res) => {
    const { quizId } = req.params;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        await prisma.quizResponse.deleteMany({
            where: { quizId },
        });
        await prisma.question.deleteMany({
            where: { quizId },
        });
        await prisma.quiz.delete({
            where: { id: quizId },
        });
        res.status(200).json({
            success: true,
            message: 'Quiz deleted successfully',
        });
    }
    catch (error) {
        logger_config_1.default.error('Error deleting quiz:', error);
        if (error instanceof http_exception_1.default) {
            res.status(error.status).json({ success: false, message: error.message });
        }
        else {
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    }
};
exports.deleteQuiz = deleteQuiz;
const deleteQuestion = async (req, res) => {
    const { quizId, questionId } = req.params;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
        });
        if (!quiz) {
            throw new http_exception_1.default(404, 'Quiz not found');
        }
        const question = await prisma.question.findUnique({
            where: { id: questionId },
        });
        if (!question) {
            throw new http_exception_1.default(404, 'Question not found');
        }
        await prisma.question.delete({
            where: { id: questionId },
        });
        res.status(200).json({
            success: true,
            message: 'Question deleted successfully',
        });
    }
    catch (error) {
        logger_config_1.default.error('Error deleting question:', error);
        if (error instanceof http_exception_1.default) {
            res.status(error.status).json({ success: false, message: error.message });
        }
        else {
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    }
};
exports.deleteQuestion = deleteQuestion;
const updateQuestion = async (req, res) => {
    const { quizId, questionId } = req.params;
    const { text, type, options, correctAnswer } = req.body;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
        });
        if (!quiz) {
            throw new http_exception_1.default(404, 'Quiz not found');
        }
        const question = await prisma.question.findUnique({
            where: { id: questionId },
        });
        if (!question) {
            throw new http_exception_1.default(404, 'Question not found');
        }
        const updatedQuestion = await prisma.question.update({
            where: { id: questionId },
            data: {
                text,
                type,
                options: options ? options.join(',') : null,
                correctAnswer,
            },
        });
        res.status(200).json({
            success: true,
            message: 'Question updated successfully',
            data: updatedQuestion,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error updating question:', error);
        if (error instanceof http_exception_1.default) {
            res.status(error.status).json({ success: false, message: error.message });
        }
        else {
            res.status(500).json({ success: false, message: 'Internal server error' });
        }
    }
};
exports.updateQuestion = updateQuestion;
const getQuizLeaderboard = async (req, res) => {
    const { quizId } = req.params;
    try {
        const leaderboard = await prisma.quizResponse.groupBy({
            by: ['userId'],
            where: { quizId },
            _max: {
                totalScore: true,
            },
        });
        const userIds = leaderboard.map(response => response.userId);
        const users = await prisma.user.findMany({
            where: {
                id: { in: userIds },
            },
            select: {
                id: true,
                name: true,
                email: true,
            },
        });
        const formattedLeaderboard = leaderboard.map(response => {
            const user = users.find(u => u.id === response.userId);
            return {
                userId: user === null || user === void 0 ? void 0 : user.id,
                userName: user === null || user === void 0 ? void 0 : user.name,
                score: response._max.totalScore || 0,
            };
        });
        res.status(200).json({
            success: true,
            data: formattedLeaderboard,
        });
    }
    catch (error) {
        logger_config_1.default.error('Error retrieving quiz leaderboard:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.getQuizLeaderboard = getQuizLeaderboard;
const getQuizResults = async (req, res) => {
    const { quizId } = req.params;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
            include: {
                questions: true,
            },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        const responses = await prisma.quizResponse.findMany({
            where: { quizId },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        const results = responses.map(response => {
            const answers = response.answers ? JSON.parse(response.answers) : {};
            const questionAnswers = quiz.questions.map(question => {
                const userAnswer = answers[question.id] || '';
                const questionMark = question.marks || quiz.pointsPerQuestion || 0;
                const isCorrect = userAnswer.toLowerCase() === (question.correctAnswer || '').toLowerCase();
                return {
                    questionId: question.id,
                    questionText: question.text,
                    userAnswer,
                    correctAnswer: question.correctAnswer,
                    isCorrect,
                    marks: questionMark,
                    marksObtained: isCorrect ? questionMark : 0,
                };
            });
            const totalMarksObtained = questionAnswers.reduce((total, q) => total + (q.marksObtained || 0), 0);
            return {
                userId: response.user.id,
                userName: response.user.name,
                userEmail: response.user.email,
                totalScore: response.totalScore || totalMarksObtained,
                completedAt: response.completedAt,
                answers: questionAnswers,
                passed: (response.totalScore || totalMarksObtained) >= (quiz.passingScore || 0),
            };
        });
        res.status(200).json({
            success: true,
            data: {
                quizId: quiz.id,
                quizTitle: quiz.title,
                totalMarks: quiz.totalMarks,
                passingScore: quiz.passingScore,
                results,
            },
        });
    }
    catch (error) {
        logger_config_1.default.error('Error retrieving quiz results:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.getQuizResults = getQuizResults;
const getPublicLeaderboard = async (req, res) => {
    const { quizId } = req.params;
    try {
        const quiz = await prisma.quiz.findUnique({
            where: { id: quizId },
            select: { id: true, title: true },
        });
        if (!quiz) {
            res.status(404).json({ success: false, message: 'Quiz not found' });
            return;
        }
        const leaderboard = await prisma.quizResponse.groupBy({
            by: ['userId'],
            where: { quizId },
            _max: {
                totalScore: true,
            },
        });
        const userIds = leaderboard.map(response => response.userId);
        const users = await prisma.user.findMany({
            where: {
                id: { in: userIds },
            },
            select: {
                id: true,
                name: true,
            },
        });
        const formattedLeaderboard = leaderboard.map(response => {
            const user = users.find(u => u.id === response.userId);
            return {
                userId: user === null || user === void 0 ? void 0 : user.id,
                userName: user === null || user === void 0 ? void 0 : user.name,
                score: response._max.totalScore || 0,
            };
        });
        formattedLeaderboard.sort((a, b) => (b.score || 0) - (a.score || 0));
        res.status(200).json({
            success: true,
            data: {
                quizId: quiz.id,
                quizTitle: quiz.title,
                leaderboard: formattedLeaderboard,
            },
        });
    }
    catch (error) {
        logger_config_1.default.error('Error retrieving public leaderboard:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.getPublicLeaderboard = getPublicLeaderboard;
const getGlobalLeaderboard = async (_req, res) => {
    try {
        const leaderboard = await prisma.quizResponse.groupBy({
            by: ['userId'],
            _sum: {
                totalScore: true,
            },
            orderBy: {
                _sum: {
                    totalScore: 'desc',
                },
            },
            take: 50,
        });
        const userIds = leaderboard.map(response => response.userId);
        const users = await prisma.user.findMany({
            where: {
                id: { in: userIds },
            },
            select: {
                id: true,
                name: true,
            },
        });
        const formattedLeaderboard = leaderboard.map(response => {
            const user = users.find(u => u.id === response.userId);
            return {
                userId: user === null || user === void 0 ? void 0 : user.id,
                userName: user === null || user === void 0 ? void 0 : user.name,
                totalScore: response._sum.totalScore || 0,
            };
        });
        res.status(200).json({
            success: true,
            data: {
                leaderboard: formattedLeaderboard,
            },
        });
    }
    catch (error) {
        logger_config_1.default.error('Error retrieving global leaderboard:', error);
        res.status(500).json({ success: false, message: 'Internal server error' });
    }
};
exports.getGlobalLeaderboard = getGlobalLeaderboard;
//# sourceMappingURL=quiz.controller.js.map