"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateSessionForContent = exports.validateContentAccess = void 0;
const client_1 = require("@prisma/client");
const http_exception_1 = __importDefault(require("../utils/http-exception"));
const logger_config_1 = __importDefault(require("../config/logger.config"));
const prisma = new client_1.PrismaClient();
const validateContentAccess = async (req, _res, next) => {
    var _a, _b, _c;
    try {
        const { contentId } = req.params;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            throw new http_exception_1.default(401, 'Unauthorized');
        }
        const content = await prisma.content.findUnique({
            where: { id: contentId },
            include: {
                canView: {
                    select: {
                        id: true,
                    },
                },
                canEdit: {
                    select: {
                        id: true,
                    },
                },
                session: {
                    select: {
                        createdById: true,
                        participants: {
                            select: {
                                id: true,
                            },
                        },
                    },
                },
            },
        });
        if (!content) {
            throw new http_exception_1.default(404, 'Content not found');
        }
        const isAdmin = ((_b = content.session) === null || _b === void 0 ? void 0 : _b.createdById) === userId;
        const canView = isAdmin ||
            content.canView.some(user => user.id === userId) ||
            ((_c = content.session) === null || _c === void 0 ? void 0 : _c.participants.some(participant => participant.id === userId));
        if (!canView) {
            throw new http_exception_1.default(403, 'You do not have permission to access this content');
        }
        if ((req.method === 'PUT' || req.method === 'DELETE') &&
            !isAdmin &&
            !content.canEdit.some(user => user.id === userId)) {
            throw new http_exception_1.default(403, 'You do not have permission to modify this content');
        }
        req.content = content.session
            ? {
                ...content,
                session: content.session
                    ? {
                        createdById: content.session.createdById,
                        participants: content.session.participants,
                    }
                    : undefined,
                canView: content.canView,
                canEdit: content.canEdit,
            }
            : undefined;
        next();
    }
    catch (error) {
        logger_config_1.default.error('Error validating content access:', error);
        next(error);
    }
};
exports.validateContentAccess = validateContentAccess;
const validateSessionForContent = async (req, _res, next) => {
    var _a;
    try {
        const { sessionId } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            throw new http_exception_1.default(401, 'Unauthorized');
        }
        const session = await prisma.session.findUnique({
            where: { id: sessionId },
            select: {
                id: true,
                createdById: true,
            },
        });
        if (!session) {
            throw new http_exception_1.default(404, 'Session not found');
        }
        if (session.createdById !== userId) {
            throw new http_exception_1.default(403, 'Only the session creator can upload content to this session');
        }
        next();
    }
    catch (error) {
        logger_config_1.default.error('Error validating session for content:', error);
        next(error);
    }
};
exports.validateSessionForContent = validateSessionForContent;
//# sourceMappingURL=content.middleware.js.map