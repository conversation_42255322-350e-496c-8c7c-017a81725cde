"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteFromCloudinary = exports.uploadToCloudinary = void 0;
const cloudinary_config_1 = __importDefault(require("../config/cloudinary.config"));
const logger_config_1 = __importDefault(require("../config/logger.config"));
const stream_1 = require("stream");
const uploadToCloudinary = async (file, folder, contentType) => {
    try {
        let resourceType = 'auto';
        if (contentType === 'VIDEO') {
            resourceType = 'video';
        }
        else if (contentType === 'IMAGE') {
            resourceType = 'image';
        }
        else if (contentType === 'PDF') {
            resourceType = 'raw';
        }
        else if (contentType === 'TEXT') {
            resourceType = 'raw';
        }
        const result = await new Promise((resolve, reject) => {
            const uploadStream = cloudinary_config_1.default.uploader.upload_stream({
                folder,
                resource_type: resourceType,
            }, (error, result) => {
                if (error) {
                    reject(error);
                }
                else {
                    resolve(result);
                }
            });
            const bufferStream = stream_1.Readable.from(file);
            bufferStream.pipe(uploadStream);
        });
        return result;
    }
    catch (error) {
        logger_config_1.default.error('Error uploading to Cloudinary:', error);
        throw error;
    }
};
exports.uploadToCloudinary = uploadToCloudinary;
const deleteFromCloudinary = async (publicId, contentType) => {
    try {
        let resourceType = 'image';
        if (contentType === 'VIDEO') {
            resourceType = 'video';
        }
        else if (contentType === 'PDF' || contentType === 'TEXT') {
            resourceType = 'raw';
        }
        const result = await cloudinary_config_1.default.uploader.destroy(publicId, {
            resource_type: resourceType,
        });
        return result;
    }
    catch (error) {
        logger_config_1.default.error('Error deleting from Cloudinary:', error);
        throw error;
    }
};
exports.deleteFromCloudinary = deleteFromCloudinary;
//# sourceMappingURL=cloudinary.service.js.map