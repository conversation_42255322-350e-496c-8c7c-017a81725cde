"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const quiz_controller_1 = require("../controllers/quiz.controller");
const auth_middleware_1 = require("../middleware/auth.middleware");
const admin_middleware_1 = require("../middleware/admin.middleware");
const validate_middleware_1 = require("../middleware/validate.middleware");
const quiz_middleware_1 = require("../middleware/quiz.middleware");
const quiz_validation_1 = require("../validations/quiz.validation");
const router = (0, express_1.Router)();
router.post('/', auth_middleware_1.authenticateToken, admin_middleware_1.isAdmin, (0, validate_middleware_1.validateRequest)(quiz_validation_1.createQuizSchema), quiz_controller_1.createQuiz);
router.get('/', auth_middleware_1.authenticateToken, quiz_controller_1.getQuizzes);
router.get('/:quizId', auth_middleware_1.authenticateToken, quiz_middleware_1.validateQuizAccess, quiz_controller_1.getQuizById);
router.post('/:quizId/questions', auth_middleware_1.authenticateToken, admin_middleware_1.isAdmin, (0, validate_middleware_1.validateRequest)(quiz_validation_1.addQuestionsSchema), quiz_controller_1.addQuestionsToQuiz);
router.post('/:quizId/join', auth_middleware_1.authenticateToken, (0, validate_middleware_1.validateRequest)(quiz_validation_1.joinQuizSchema), quiz_controller_1.joinQuiz);
router.post('/:quizId/submit', auth_middleware_1.authenticateToken, quiz_middleware_1.validateQuizAccess, (0, validate_middleware_1.validateRequest)(quiz_validation_1.submitQuizResponseSchema), quiz_controller_1.submitQuizResponse);
router.put('/:quizId', auth_middleware_1.authenticateToken, admin_middleware_1.isAdmin, (0, validate_middleware_1.validateRequest)(quiz_validation_1.updateQuizSchema), quiz_controller_1.updateQuiz);
router.put('/:quizId/questions/:questionId', auth_middleware_1.authenticateToken, admin_middleware_1.isAdmin, (0, validate_middleware_1.validateRequest)(quiz_validation_1.updateQuestionSchema), quiz_controller_1.updateQuestion);
router.delete('/:quizId', auth_middleware_1.authenticateToken, admin_middleware_1.isAdmin, quiz_controller_1.deleteQuiz);
router.delete('/:quizId/questions/:questionId', auth_middleware_1.authenticateToken, admin_middleware_1.isAdmin, quiz_controller_1.deleteQuestion);
router.get('/:quizId/leaderboard', auth_middleware_1.authenticateToken, quiz_middleware_1.validateQuizAccess, quiz_controller_1.getQuizLeaderboard);
router.get('/:quizId/results', auth_middleware_1.authenticateToken, admin_middleware_1.isAdmin, quiz_controller_1.getQuizResults);
router.get('/leaderboard', quiz_controller_1.getGlobalLeaderboard);
router.get('/public-leaderboard/:quizId', quiz_controller_1.getPublicLeaderboard);
exports.default = router;
//# sourceMappingURL=quiz.routes.js.map